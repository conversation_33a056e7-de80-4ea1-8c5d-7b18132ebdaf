import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { FC, SVGProps } from 'react';

//theme, assets, utils, actions and interface imports
import { colors } from '../../theme/theme';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';

interface TGCustomIconButtonProps {
    text: string;
    onPress: () => void;
    btnStyle?: object;
    disabledStyle?: object;
    textStyle?: object;
    loading?: boolean;
    disabled?: boolean;
    isRightIcon?: boolean;
    Icon?: FC<SVGProps<SVGSVGElement>> | undefined;
    iconHeight?: number;
    iconWidth?: number;
}
const TGCustomIconButton = ({
    text = '',
    onPress = () => {},
    btnStyle = {},
    disabledStyle = {},
    textStyle = {},
    loading = false,
    disabled = false,
    isRightIcon = false,
    Icon = undefined,
    iconHeight = Size.SIZE_20,
    iconWidth = Size.SIZE_20,
}: TGCustomIconButtonProps) => {
    return (
        <TouchableOpacity
            style={[styles.buttonSave, btnStyle, Object.keys(disabledStyle).length > 0 ? styles.disabledBtn : {}]}
            onPress={onPress}
            disabled={disabled}>
            {!isRightIcon && Icon && <Icon height={iconHeight} width={iconWidth} />}
            {loading ? (
                <ActivityIndicator color="white" />
            ) : (
                <Text style={[disabledStyle && disabled ? styles.disabledBtnText : styles.btnTextStyle, textStyle]}>
                    {text}
                </Text>
            )}
            {isRightIcon && Icon && <Icon />}
        </TouchableOpacity>
    );
};

export default TGCustomIconButton;

const styles = StyleSheet.create({
    buttonSave: {
        backgroundColor: colors.darkteal,
        width: '50%',
        paddingVertical: 15,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        columnGap: Spacing.SCALE_8,
    },
    btnTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_18,
        color: colors.dark_charcoal,
    },
    disabledBtnText: {
        color: colors.lightShadeGray,
    },
    disabledBtn: {
        backgroundColor: 'rgba(196, 196, 196, 0.4)',
    },
});
