import React, { useContext } from 'react';
import { Platform, Pressable, SafeAreaView, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';

import { Back } from '../../assets/images/svg';
import { GlobalContext } from '../../context/contextApi';
import CreateGroup from '../../assets/svg/CreateGroup.svg';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import FAQTealIcon from '../../assets/svg/FAQTealIcon.svg';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const StreamHeader = ({
    screenName,
    showCreateGroupIcon = false,
    onClickSvgIcon = () => { },
    customHeaderTextStyle = '',
    headerAligned = 'center',
    showFAQIcon = false,
    onFAQIconClick = () => {},
    onPressGoBack,
}) => {
    const insets = useSafeAreaInsets();
    const { state, actions } = useContext(GlobalContext);
    const navigation = useNavigation();
    // Object which define on which number of tab will active after clock on back button
    const handleTabNavigation = {
        'Edit Offer': 2,
        'Create Request': 0,
        'Profile Info': 2,
        'Share Message To': 3,
        'Edit Request': 1,
    };

    return (
        <>
            <StatusBar barStyle="dark-content" />
            <View style={[styles.container, { paddingTop: Platform.OS === 'ios' ? Spacing.SCALE_20 : insets.top }]}>
                <View style={styles.headerWrapper}>
                    <Pressable
                        style={styles.backButtonWrapper}
                        onPress={() => {
                            if (screenName === 'Create Request') {
                                navigation.goBack();
                            } else {
                                if (onPressGoBack) {
                                    onPressGoBack();
                                } else {
                                    actions.setShouldDetailBottomSheetOpen(true);
                                    actions.userProfile({});
                                    actions.setCurrentTab(state.currentTab); // Set current tab in context
                                    actions?.setMyTgGroupDetails({});
                                    navigation.goBack();
                                }
                            }
                        }}>
                        <Back />
                    </Pressable>
                    <View
                        style={[
                            styles.screenNameWrapper,
                            ([
                                'Profile Info',
                                'Club Visibility Details',
                                'My Favourite Clubs',
                                'Favourite Clubs',
                                'Create New Request',
                                'Request Flow impact on NGV',
                            ].includes(screenName) ||
                                ['left'].includes(headerAligned)) && {
                                alignItems: 'flex-start',
                                paddingLeft: Spacing.SCALE_20,
                            },
                        ]}>
                        <Text style={[styles.text, customHeaderTextStyle]}>{screenName}</Text>
                    </View>
                    {showCreateGroupIcon && (
                        <Pressable style={styles.svgIconWrapper} onPress={onClickSvgIcon}>
                            <CreateGroup height={Size.SIZE_30} width={Size.SIZE_30} />
                        </Pressable>
                    )}

                    {showFAQIcon && (
                        <TouchableOpacity style={styles.svgIconWrapper} onPress={onFAQIconClick}>
                            <FAQTealIcon height={Size.SIZE_30} width={Size.SIZE_30} />
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        </>
    );
};

export default StreamHeader;

const styles = StyleSheet.create({
    container: {
        height: Platform.OS === 'ios' ? Size.SIZE_90 : Size.SIZE_50,
        width: '100%',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        paddingHorizontal: Spacing.SCALE_16,
        paddingLeft: 5,
    },
    text: {
        fontSize: Typography.FONT_SIZE_18,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_20,
        color: 'rgba(51, 51, 51, 1)',
    },
    headerWrapper: {
        flex: 1,
        display: 'flex',
        flexDirection: 'row',
    },
    backButtonWrapper: {
        width: Spacing.SCALE_20,
        justifyContent: 'center',
        alignItems: 'center',
        paddingLeft: Spacing.SCALE_15,
    },
    screenNameWrapper: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    svgIconWrapper: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingRight: Spacing.SCALE_1,
    },
});
