import React, { useContext, useEffect } from 'react';
import { View, Text, TouchableOpacity, Keyboard, StyleSheet } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation } from '@react-navigation/native';

import { AuthContext } from '../../context/AuthContext';
import { CLUB_DETAILS_MAP, placeDetails } from '../../service/EndPoint';
import { fetcher } from '../../service/fetcher';
import { colors } from '../../theme/theme';
import { GlobalContext } from '../../context/contextApi';
import { Size, Spacing } from '../../utils/responsiveUI';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';

export default function MapSearchResults({
    searchedClubList = [],
    locations = [],
    searchingState = [null, () => {}],
    searchInputState = [null, () => {}],
    setSelectedClub,
    setMapLocation,
    setActiveView,
    changeRegion,
    setCenterCoordinates,
    setSearchType,
    activeView,
}) {
    const { user } = useContext(AuthContext);
    const [searchValue, setSearchValue] = searchInputState;
    const [searching, setSearching] = searchingState;
    const { state, actions } = useContext(GlobalContext);
    const navigation = useNavigation();
    const animatedOpacity = useSharedValue(0);

    useEffect(() => {
        setTimeout(() => {
            animatedOpacity.value = withTiming(1, { duration: 300 });
        }, 300);
    }, [searchValue]);

    const animatedStyle = useAnimatedStyle(() => {
        return {
            opacity: animatedOpacity.value,
        };
    });

    //Function used for get location
    const getLocationDetails = (placeId, type) => {
        fetch(`${placeDetails}${placeId}`)
            .then((data) =>
                data.json().then(({ result }) => {
                    if (result?.geometry) {
                        setMapLocation(result?.geometry, type);
                        actions?.setMapCurrentState(result?.geometry?.location);
                    }
                }),
            )
            .catch(() => {});
    };

    const handleClubPress = ({ id, properties: { color } }) => {
        const body = {
            userId: user?.id,
            clubId: id,
            clubColor: color,
        };
        fetcher({
            endpoint: CLUB_DETAILS_MAP,
            method: 'POST',
            body,
        })
            .then((data) => {
                const { lat, lng } = data?.clubs;
                if (lat && lng) {
                    setActiveView('MAP');
                    setSearching(false);
                    changeRegion(lat - 0.03, lng, true, 11);
                    actions?.setMapCurrentState({ lat: lat - 0.03, lng: lng });
                    setSelectedClub(data);
                    actions.setShouldDetailBottomSheetOpen(true);
                }
            })
            .catch(() => {});
    };

    //function used for searched data pressed
    const onItemPress = (name, data) => {
        actions.setIsMapSearchActive(false);
        if (name === 'club') {
            actions.setMapCurrentClub(data);
            setSearchType('club');
            actions?.setClubDetails(data);
            handleClubPress(data);
        } else {
            setSearchType('location');
            getLocationDetails(
                data?.place_id,
                data?.types?.includes('locality')
                    ? 'city'
                    : data?.types?.includes('administrative_area_level_1')
                    ? 'state'
                    : 'country',
            );
        }
        setSearchValue('');
        setSearching(false);
        Keyboard.dismiss();
    };

    return searching ? (
        <Animated.View style={[styles.container, animatedStyle]}>
            <KeyboardAwareScrollView
                bounces={false}
                keyboardShouldPersistTaps="handled"
                style={{
                    position: 'absolute',
                    top: activeView === 'MAP' ? Spacing.SCALE_45 : -Spacing.SCALE_6,
                    backgroundColor: colors.modalBackGroundColor,
                    width: Size.SIZE_340,
                    alignSelf: 'center',
                    borderRadius: Size.SIZE_10,
                }}>
                <View style={{ flex: 1, backgroundColor: colors.whiteRGB }}>
                    {searchedClubList && searchedClubList?.length > 0 && (
                        <>
                            <Text style={styles.title}>Clubs</Text>
                            {searchedClubList.slice(0, 10).map((club) => (
                                <TouchableOpacity onPress={() => onItemPress('club', club)}>
                                    <Text style={styles.result}>{club?.properties?.name}</Text>
                                </TouchableOpacity>
                            ))}
                        </>
                    )}
                    {locations && locations?.length > 0 && (
                        <>
                            <Text style={styles.title}>Locations</Text>
                            {locations.map((location) => (
                                <TouchableOpacity onPress={() => onItemPress('location', location)}>
                                    <Text style={styles.result}>{location?.description}</Text>
                                </TouchableOpacity>
                            ))}
                        </>
                    )}
                    {locations?.length === 0 && searchedClubList?.length === 0 ? (
                        <Text
                            style={{
                                backgroundColor: colors.lightgray,
                                color: colors.darkgray,
                                fontFamily: 'Ubuntu-Regular',
                                paddingHorizontal: 15,
                                paddingVertical: 10,
                                textAlign: 'center',
                                width: '100%',
                            }}>
                            No Result Found
                        </Text>
                    ) : null}
                </View>
            </KeyboardAwareScrollView>
        </Animated.View>
    ) : null;
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.modalBackGroundColor,
        height: '100%',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 100,
    },
    title: {
        backgroundColor: colors.lightgray,
        color: colors.darkgray,
        fontFamily: 'Ubuntu-Regular',
        paddingHorizontal: Spacing.SCALE_15,
        paddingVertical: Spacing.SCALE_7,
        fontSize: Size.SIZE_12,
    },
    result: {
        backgroundColor: 'white',
        fontFamily: 'Ubuntu-Regular',
        paddingHorizontal: Spacing.SCALE_15,
        paddingVertical: Spacing.SCALE_8,
        color: colors.lightBlack,
        fontSize: Size.SIZE_12,
    },
});
