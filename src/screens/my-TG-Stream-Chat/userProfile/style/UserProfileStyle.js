import { StyleSheet } from "react-native";

import { Size, Spacing, Typography } from "../../../../utils/responsiveUI";
import { colors } from "../../../../theme/theme";

export default styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'white',
    },
    topSpace: {
        marginTop: 10,
        backgroundColor: colors.lightgray,
        height: Size.SIZE_12
    },
    box: {
        flex: 1,
        backgroundColor: 'rgba(242, 242, 242, 1)',
        borderTopRightRadius: Size.SIZE_10,
        borderTopLeftRadius: Size.SIZE_10,
    },
    aboutWrapper: {
        backgroundColor: 'rgba(255, 255, 255, 1)',
        padding: Spacing.SCALE_20,
        marginTop: Spacing.SCALE_12,
        borderRadius: Size.SIZE_4
    },
    aboutHeader: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_22,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(128, 128, 128, 1)',
    },
    aboutText: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_22,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(0, 0, 0, 1)'
    },
    blockIconWrapper: {
        paddingVertical: Spacing.SCALE_28,
        paddingHorizontal: Spacing.SCALE_20,
        paddingTop: 15,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.white
    },
    blockTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_18,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(224, 94, 82, 1)',
        marginLeft: Spacing.SCALE_10
    },
    unBlockTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_18,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(9, 128, 137, 1)',
        marginLeft: Spacing.SCALE_10
    },
    loaderStyle: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        right: 0,
        left: 0,
        justifyContent: 'center',
        alignItems: 'center'
    },
    blockText: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(0, 0, 0, 1)',
        marginTop: Spacing.SCALE_28
    },
    GroupDetailBlockIconStyle: {
        alignSelf: 'center',
        marginTop: Spacing.SCALE_50
    }
})