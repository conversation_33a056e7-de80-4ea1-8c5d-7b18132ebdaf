import { useNavigation } from "@react-navigation/native";
import React, { useState } from "react";
import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";

import { colors } from "../../../../theme/theme";
import { Size, Spacing, Typography } from "../../../../utils/responsiveUI";
import TGAmbassadorLable from "../../../../forms/profile/views/TGAmbassadorLable";

const ProfileIcon = ({ profilePhoto, name, tg_ambassador_visibility = false }) => {
    const navigation = useNavigation()
    const [showBadgeText, setShowBadgeText] = useState(false)

    const handlePress = () => {
        if (profilePhoto) {
            navigation.navigate('ShowUserDp', { profilePhoto: profilePhoto })
        }
    }

    const showFullTitle = (value) => {
        setShowBadgeText(value)

        if (value && !showBadgeText) {
            setTimeout(() => {
                setShowBadgeText(false)
            }, 2000)
        }
    }

    return <TouchableOpacity style={styles.profileWrapper} onPress={handlePress}>

        {tg_ambassador_visibility && <TouchableOpacity onPress={() => { showFullTitle(true) }} style={{zIndex: 100}}>
            <TGAmbassadorLable containerStyle={[{
                marginVertical: 0,
                position: 'absolute',

            },
            showBadgeText ? {
                paddingVertical: Spacing.SCALE_3,
                paddingHorizontal: Spacing.SCALE_3,
                paddingRight: Spacing.SCALE_10
            }
                : { paddingVertical: Spacing.SCALE_3, },
            profilePhoto ? {
                left: 12,
                top: -5,
            } : {
                left: 15,
                top: -20,
            }
            ]}
                showBadgeText={showBadgeText}
                iconHeight={18}
                iconWidth={18}
            />
        </TouchableOpacity>}
        {


            (profilePhoto) ? <Image
                source={{ uri: profilePhoto }}
                style={styles.imageStyle}
            />
                :
                <>{
                    name ?
                        <View style={styles.imageTextWrapper}>
                            <Text style={styles.imageText}>{name?.trim(' ')[0]}</Text>
                        </View>
                        :
                        null
                }
                </>
        }
    </TouchableOpacity>
}

export default ProfileIcon

const styles = StyleSheet.create({
    imageTextWrapper: {
        width: Size.SIZE_70,
        alignItems: 'center'
    },
    imageText: {
        fontSize: Typography.FONT_SIZE_30,
        fontFamily: 'Ubuntu',
        fontWeight: '500',
        color: 'rgba(9, 128, 137, 1)',
        textAlign: 'center',
        textTransform: 'capitalize'
    },
    profileWrapper: {
        borderRadius: Size.SIZE_100,
        minWidth: Size.SIZE_70,
        minHeight: Size.SIZE_70,
        backgroundColor: colors.groupInfoGrayIcon,
        alignSelf: 'center',
        marginTop: Spacing.SCALE_24,
        alignItems: 'center',
        justifyContent: 'center'
    },
    imageStyle: {
        height: Size.SIZE_70,
        width: Size.SIZE_70,
        borderRadius: Size.SIZE_100,
    },
    loaderWrapper: {
        position: 'absolute',
        left: 0,
        right: 0
    },
    name: {
        fontSize: Size.SIZE_16,
        fontWeight: '500',
        lineHeight: Size.SIZE_30,
        fontFamily: 'Ubuntu',
        color: 'rgba(15, 24, 40, 1)',
        alignSelf: 'center',
        marginTop: Spacing.SCALE_9
    },
    memberShip: {
        fontSize: Size.SIZE_13,
        fontWeight: '400',
        lineHeight: Size.SIZE_15,
        fontFamily: 'Ubuntu',
        color: 'rgba(128, 128, 128, 1)',
        alignSelf: 'center',
        marginTop: Spacing.SCALE_6
    }
})