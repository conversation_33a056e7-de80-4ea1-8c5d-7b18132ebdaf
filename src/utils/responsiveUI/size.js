import {Dimensions, Platform, StatusBar} from 'react-native';
import {scaleSize} from './mixins'

export const {width: WIDTH, height: HEIGHT} = Dimensions.get('screen');

export const HEADER_HEIGHT = Platform.OS === 'android' ? 50 : 40;

export const IS_BIG = HEIGHT > 812;

export const TOP = 
Platform.OS === 'android' 
        ? StatusBar.currentHeight || 0
        : HEIGHT >= 812
        ? 44
        : 20;

export {scaleSize};

export const SIZE_1 = scaleSize(1);
export const SIZE_2 = scaleSize(2);
export const SIZE_4 = scaleSize(4);
export const SIZE_5 = scaleSize(5);
export const SIZE_6 = scaleSize(6);
export const SIZE_7 = scaleSize(7);
export const SIZE_8 = scaleSize(8);
export const SIZE_9 = scaleSize(9);
export const SIZE_10 = scaleSize(10);
export const SIZE_11 = scaleSize(11);
export const SIZE_12 = scaleSize(12);
export const SIZE_13 = scaleSize(13);
export const SIZE_14 = scaleSize(14);
export const SIZE_15 = scaleSize(15);
export const SIZE_16 = scaleSize(16);
export const SIZE_17 = scaleSize(17);
export const SIZE_18 = scaleSize(18);
export const SIZE_19 = scaleSize(19);
export const SIZE_20 = scaleSize(20);
export const SIZE_21 = scaleSize(21);
export const SIZE_22 = scaleSize(22);
export const SIZE_24 = scaleSize(24);
export const SIZE_25 = scaleSize(25);
export const SIZE_26 = scaleSize(26);
export const SIZE_27 = scaleSize(27);
export const SIZE_28 = scaleSize(28);
export const SIZE_30 = scaleSize(30);
export const SIZE_34 = scaleSize(34);
export const SIZE_32 = scaleSize(32);
export const SIZE_36 = scaleSize(36);
export const SIZE_37 = scaleSize(37);
export const SIZE_38 = scaleSize(38);
export const SIZE_40 = scaleSize(40);
export const SIZE_42 = scaleSize(42);
export const SIZE_45 = scaleSize(45);
export const SIZE_46 = scaleSize(46);
export const SIZE_49 = scaleSize(49);
export const SIZE_50 = scaleSize(50);
export const SIZE_54 = scaleSize(54);
export const SIZE_56 = scaleSize(56);
export const SIZE_58 = scaleSize(58);
export const SIZE_60 = scaleSize(60);
export const SIZE_67 = scaleSize(67);
export const SIZE_64 = scaleSize(64);
export const SIZE_70 = scaleSize(70);
export const SIZE_72 = scaleSize(72);
export const SIZE_74 = scaleSize(74);
export const SIZE_78 = scaleSize(78);
export const SIZE_80 = scaleSize(80);
export const SIZE_90 = scaleSize(90);
export const SIZE_92 = scaleSize(92);
export const SIZE_94 = scaleSize(94);
export const SIZE_96 = scaleSize(96);
export const SIZE_100 = scaleSize(100);
export const SIZE_102 = scaleSize(102);
export const SIZE_105 = scaleSize(105);
export const SIZE_116 = scaleSize(116);
export const SIZE_119 = scaleSize(119);
export const SIZE_120 = scaleSize(120);
export const SIZE_125 = scaleSize(125);
export const SIZE_127 = scaleSize(127);
export const SIZE_130 = scaleSize(130);
export const SIZE_133 = scaleSize(133);
export const SIZE_135 = scaleSize(135);
export const SIZE_131 = scaleSize(131);
export const SIZE_138 = scaleSize(138);
export const SIZE_140 = scaleSize(140);
export const SIZE_145 = scaleSize(145);
export const SIZE_150 = scaleSize(150);
export const SIZE_156 = scaleSize(156);
export const SIZE_160 = scaleSize(160);
export const SIZE_170 = scaleSize(170);
export const SIZE_175 = scaleSize(175);
export const SIZE_180 = scaleSize(180);
export const SIZE_183 = scaleSize(183);
export const SIZE_189 = scaleSize(189);
export const SIZE_190 = scaleSize(190);
export const SIZE_192 = scaleSize(192);
export const SIZE_200 = scaleSize(200);
export const SIZE_201 = scaleSize(201);
export const SIZE_210 = scaleSize(210);
export const SIZE_212 = scaleSize(212);
export const SIZE_220 = scaleSize(220);
export const SIZE_239 = scaleSize(239);
export const SIZE_240 = scaleSize(240);
export const SIZE_246 = scaleSize(246);
export const SIZE_230 = scaleSize(230);
export const SIZE_250 = scaleSize(250);
export const SIZE_260 = scaleSize(260);
export const SIZE_259 = scaleSize(259);
export const SIZE_265 = scaleSize(265);
export const SIZE_268 = scaleSize(268);
export const SIZE_272 = scaleSize(272);
export const SIZE_270 = scaleSize(270);
export const SIZE_280 = scaleSize(280);
export const SIZE_286 = scaleSize(286);
export const SIZE_290 = scaleSize(290);
export const SIZE_300 = scaleSize(300);
export const SIZE_310 = scaleSize(310);
export const SIZE_320 = scaleSize(320);
export const SIZE_335 = scaleSize(335);
export const SIZE_340 = scaleSize(340);
export const SIZE_350 = scaleSize(350);
export const SIZE_360 = scaleSize(360);
export const SIZE_420 = scaleSize(420);
export const SIZE_500 = scaleSize(500);
export const SIZE_550 = scaleSize(550);
export const SIZE_580 = scaleSize(580);
export const SIZE_620 = scaleSize(620);
export const SIZE_640 = scaleSize(640);
export const SIZE_650 = scaleSize(650);